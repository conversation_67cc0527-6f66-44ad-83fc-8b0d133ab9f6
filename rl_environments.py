"""
Pro Trader RL 强化学习环境
包含Buy Knowledge RL和Sell Knowledge RL环境
"""

import numpy as np
import pandas as pd
import gym
from gym import spaces
from typing import Dict, Tuple, List, Optional, Any
import logging
from config import *
from data_preprocessing import DataPreprocessor

logger = logging.getLogger(__name__)


class BuyKnowledgeRLEnv(gym.Env):
    """
    Buy Knowledge RL环境
    根据论文第7页的描述实现
    
    状态：69维归一化特征向量
    动作：2个（买入概率>=10%, 买入概率<10%）
    奖励：基于实际收益率是否达到10%
    """
    
    def __init__(self, data: pd.DataFrame, mode: str = 'train'):
        """
        初始化Buy Knowledge RL环境
        
        Args:
            data: 包含处理后数据的DataFrame
            mode: 'train' 或 'test'
        """
        super(BuyKnowledgeRLEnv, self).__init__()
        
        self.mode = mode
        self.data = data[data['buy_signal'] == True].reset_index(drop=True)
        
        # 定义动作空间（2个离散动作）
        self.action_space = spaces.Discrete(2)
        
        # 定义观察空间（69维连续特征）
        self.observation_space = spaces.Box(
            low=-np.inf, 
            high=np.inf, 
            shape=(69,), 
            dtype=np.float32
        )
        
        # 环境状态
        self.current_step = 0
        self.max_steps = len(self.data)
        
        # 用于平衡数据集
        if mode == 'train':
            self._balance_dataset()
        
        logger.info(f"Buy Knowledge RL环境初始化完成，数据量: {len(self.data)}")
    
    def _balance_dataset(self):
        """
        平衡数据集，确保10%以上收益和10%以下收益的样本数量相同
        """
        # 计算每个样本的收益率标签
        labels = []
        for idx, row in self.data.iterrows():
            future_return = self._calculate_future_return(idx)
            labels.append(1 if future_return >= 0.1 else 0)
        
        self.data['label'] = labels
        
        # 平衡采样
        positive_samples = self.data[self.data['label'] == 1]
        negative_samples = self.data[self.data['label'] == 0]
        
        min_samples = min(len(positive_samples), len(negative_samples))
        
        if min_samples > 0:
            balanced_data = pd.concat([
                positive_samples.sample(n=min_samples, random_state=42),
                negative_samples.sample(n=min_samples, random_state=42)
            ])
            self.data = balanced_data.sample(frac=1, random_state=42).reset_index(drop=True)
            
            logger.info(f"数据集平衡完成: 正样本{min_samples}, 负样本{min_samples}")
    
    def _calculate_future_return(self, idx: int) -> float:
        """
        计算未来收益率（从买入信号到卖出信号）
        根据论文公式(19)
        """
        row = self.data.iloc[idx]
        ts_code = row['ts_code']
        buy_date = row['trade_date']
        buy_price = row['open']  # 次日开盘价买入
        
        # 查找对应的卖出信号
        future_data = self.data[
            (self.data['ts_code'] == ts_code) & 
            (self.data['trade_date'] > buy_date)
        ]
        
        if len(future_data) > 0:
            # 找到第一个卖出信号或120天后
            sell_signal = future_data[future_data['sell_signal'] == True].head(1)
            
            if len(sell_signal) > 0:
                sell_price = sell_signal.iloc[0]['open']
                return (sell_price - buy_price) / buy_price
            else:
                # 如果没有卖出信号，使用120天后的价格
                if len(future_data) >= 120:
                    sell_price = future_data.iloc[119]['close']
                    return (sell_price - buy_price) / buy_price
        
        return 0.0
    
    def reset(self) -> np.ndarray:
        """重置环境"""
        self.current_step = 0
        
        if self.mode == 'train':
            # 训练模式下随机选择起始点
            self.current_step = np.random.randint(0, max(1, self.max_steps - 1))
        
        return self._get_observation()
    
    def _get_observation(self) -> np.ndarray:
        """获取当前观察状态（69维特征向量）"""
        if self.current_step >= len(self.data):
            return np.zeros(69)
        
        row = self.data.iloc[self.current_step]
        features = np.array(row['rl_features'])
        
        return features.astype(np.float32)
    
    def step(self, action: int) -> Tuple[np.ndarray, float, bool, Dict]:
        """
        执行一步环境交互
        
        Args:
            action: 0（预测收益<10%）或 1（预测收益>=10%）
        
        Returns:
            observation: 下一个状态
            reward: 奖励
            done: 是否结束
            info: 额外信息
        """
        # 计算实际收益率
        actual_return = self._calculate_future_return(self.current_step)
        actual_label = 1 if actual_return >= 0.1 else 0
        
        # 计算奖励（根据论文第7页的4个场景）
        reward = 0.0
        if action == 1 and actual_label == 1:
            # 场景1：正确预测高收益
            reward = 1.0
        elif action == 1 and actual_label == 0:
            # 场景2：错误预测高收益
            reward = 0.0
        elif action == 0 and actual_label == 0:
            # 场景3：正确预测低收益
            reward = 1.0
        elif action == 0 and actual_label == 1:
            # 场景4：错过高收益机会
            reward = 0.0
        
        # 更新步数
        self.current_step += 1
        
        # 检查是否结束
        done = self.current_step >= self.max_steps
        
        # 获取下一个观察
        observation = self._get_observation()
        
        # 额外信息
        info = {
            'actual_return': actual_return,
            'actual_label': actual_label,
            'predicted_label': action,
            'correct': action == actual_label
        }
        
        return observation, reward, done, info
    
    def render(self, mode='human'):
        """渲染环境（可选）"""
        pass


class SellKnowledgeRLEnv(gym.Env):
    """
    Sell Knowledge RL环境
    根据论文第8页的描述实现
    
    状态：70维特征向量（69维基础特征 + 当前收益率）
    动作：2个（卖出，持有）
    奖励：基于相对收益率计算
    """
    
    def __init__(self, data: pd.DataFrame, mode: str = 'train'):
        """
        初始化Sell Knowledge RL环境
        
        Args:
            data: 包含处理后数据的DataFrame
            mode: 'train' 或 'test'
        """
        super(SellKnowledgeRLEnv, self).__init__()
        
        self.mode = mode
        self.data = data
        
        # 定义动作空间（2个离散动作：0=持有，1=卖出）
        self.action_space = spaces.Discrete(2)
        
        # 定义观察空间（70维连续特征）
        self.observation_space = spaces.Box(
            low=-np.inf,
            high=np.inf,
            shape=(70,),
            dtype=np.float32
        )
        
        # 环境状态
        self.current_episode_data = None
        self.current_step = 0
        self.entry_price = 0
        self.buy_date = None
        self.ts_code = None
        
        # 获取所有买入信号
        self.buy_signals = data[data['buy_signal'] == True].reset_index(drop=True)
        self.current_buy_idx = 0
        
        logger.info(f"Sell Knowledge RL环境初始化完成，买入信号数: {len(self.buy_signals)}")
    
    def reset(self) -> np.ndarray:
        """重置环境，选择一个新的买入点开始"""
        if self.mode == 'train':
            # 训练模式下随机选择买入点
            self.current_buy_idx = np.random.randint(0, len(self.buy_signals))
        else:
            # 测试模式下顺序选择
            self.current_buy_idx = (self.current_buy_idx + 1) % len(self.buy_signals)
        
        # 获取买入信息
        buy_signal = self.buy_signals.iloc[self.current_buy_idx]
        self.ts_code = buy_signal['ts_code']
        self.buy_date = buy_signal['trade_date']
        self.entry_price = buy_signal['open']
        
        # 获取买入后120天的数据
        future_data = self.data[
            (self.data['ts_code'] == self.ts_code) &
            (self.data['trade_date'] > self.buy_date)
        ].head(120)
        
        if len(future_data) == 0:
            # 如果没有未来数据，重新选择
            return self.reset()
        
        self.current_episode_data = future_data.reset_index(drop=True)
        self.current_step = 0
        
        return self._get_observation()
    
    def _get_observation(self) -> np.ndarray:
        """获取当前观察状态（70维特征向量）"""
        if self.current_step >= len(self.current_episode_data):
            return np.zeros(70)
        
        row = self.current_episode_data.iloc[self.current_step]
        
        # 69维基础特征
        features = np.array(row['rl_features'])
        
        # 计算当前收益率（根据论文公式20）
        current_price = row['open']
        current_return = (current_price - self.entry_price) / self.entry_price
        
        # 组合成70维特征
        observation = np.concatenate([features, [current_return]])
        
        return observation.astype(np.float32)
    
    def _calculate_relative_reward(self, current_return: float) -> float:
        """
        计算相对奖励（根据论文公式21）
        在所有达到10%收益的时间点中，最高收益获得+2，最低获得+1
        """
        if current_return < 0.1:
            return 0.0
        
        # 找出所有达到10%收益的时间点
        high_return_points = []
        for i in range(len(self.current_episode_data)):
            price = self.current_episode_data.iloc[i]['open']
            ret = (price - self.entry_price) / self.entry_price
            if ret >= 0.1:
                high_return_points.append(ret)
        
        if len(high_return_points) == 0:
            return 1.0
        
        # 计算相对排名
        rank = sorted(high_return_points, reverse=True).index(current_return) + 1
        relative_reward = (len(high_return_points) - rank + 1) / len(high_return_points) + 1
        
        return relative_reward
    
    def step(self, action: int) -> Tuple[np.ndarray, float, bool, Dict]:
        """
        执行一步环境交互
        
        Args:
            action: 0（持有）或 1（卖出）
        
        Returns:
            observation: 下一个状态
            reward: 奖励
            done: 是否结束
            info: 额外信息
        """
        if self.current_step >= len(self.current_episode_data):
            return np.zeros(70), 0.0, True, {}
        
        # 计算当前收益率
        current_price = self.current_episode_data.iloc[self.current_step]['open']
        current_return = (current_price - self.entry_price) / self.entry_price
        
        # 计算奖励（根据论文第8页的4个场景）
        reward = 0.0
        done = False
        
        if action == 1:  # 卖出
            if current_return >= 0.1:
                # 场景1：在高收益点卖出
                reward = self._calculate_relative_reward(current_return)
            else:
                # 场景2：在低收益点卖出
                reward = -1.0
            done = True  # 卖出后结束
            
        else:  # 持有
            if current_return >= 0.1:
                # 场景4：错过高收益卖出机会
                reward = -1.0
            else:
                # 场景3：正确持有
                reward = 0.5
        
        # 更新步数
        self.current_step += 1
        
        # 检查是否到达120天
        if self.current_step >= 120:
            done = True
        
        # 获取下一个观察
        observation = self._get_observation()
        
        # 额外信息
        info = {
            'current_return': current_return,
            'action': 'sell' if action == 1 else 'hold',
            'step': self.current_step,
            'done_reason': 'sold' if action == 1 else ('timeout' if done else 'continue')
        }
        
        return observation, reward, done, info
    
    def render(self, mode='human'):
        """渲染环境（可选）"""
        pass


class StopLossRule:
    """
    止损规则模块
    根据论文第8页的描述实现
    """
    
    def __init__(self, stop_loss_on_dips: float = -0.1, 
                 stop_loss_on_sideways_days: int = 20,
                 stop_loss_on_sideways_threshold: float = -0.1):
        """
        初始化止损规则
        
        Args:
            stop_loss_on_dips: 下跌止损阈值
            stop_loss_on_sideways_days: 横盘止损天数
            stop_loss_on_sideways_threshold: 横盘止损阈值
        """
        self.stop_loss_on_dips = stop_loss_on_dips
        self.stop_loss_on_sideways_days = stop_loss_on_sideways_days
        self.stop_loss_on_sideways_threshold = stop_loss_on_sideways_threshold
        
        logger.info(f"止损规则初始化: 下跌止损{stop_loss_on_dips*100}%, "
                   f"横盘{stop_loss_on_sideways_days}天止损")
    
    def check_stop_loss(self, returns_history: List[float], 
                       current_return: float) -> Tuple[bool, str]:
        """
        检查是否触发止损
        
        Args:
            returns_history: 历史收益率列表
            current_return: 当前收益率
        
        Returns:
            (是否止损, 止损原因)
        """
        # 检查下跌止损
        if current_return <= self.stop_loss_on_dips:
            return True, "stop_loss_on_dips"
        
        # 检查横盘止损
        if len(returns_history) >= self.stop_loss_on_sideways_days:
            recent_returns = returns_history[-self.stop_loss_on_sideways_days:]
            if all(r <= self.stop_loss_on_sideways_threshold for r in recent_returns):
                return True, "stop_loss_on_sideways"
        
        return False, ""
    
    def apply_stop_loss(self, data: pd.DataFrame, entry_price: float, 
                        entry_date: pd.Timestamp) -> Dict[str, Any]:
        """
        应用止损规则到一次交易
        
        Args:
            data: 股票数据
            entry_price: 入场价格
            entry_date: 入场日期
        
        Returns:
            止损结果信息
        """
        # 获取入场后的数据
        future_data = data[data.index > entry_date].head(120)
        
        returns_history = []
        
        for idx, row in future_data.iterrows():
            current_price = row['close']
            current_return = (current_price - entry_price) / entry_price
            
            # 检查止损
            should_stop, reason = self.check_stop_loss(returns_history, current_return)
            
            if should_stop:
                return {
                    'stop_loss': True,
                    'reason': reason,
                    'exit_date': row.name,
                    'exit_price': row['open'],  # 次日开盘价止损
                    'return': current_return,
                    'holding_days': len(returns_history) + 1
                }
            
            returns_history.append(current_return)
        
        # 未触发止损
        return {
            'stop_loss': False,
            'reason': None,
            'exit_date': None,
            'exit_price': None,
            'return': returns_history[-1] if returns_history else 0,
            'holding_days': len(returns_history)
        }


# 测试代码
if __name__ == "__main__":
    # 加载数据
    preprocessor = DataPreprocessor()
    data = preprocessor.process_all_data()
    
    # 创建Buy Knowledge RL环境
    buy_env = BuyKnowledgeRLEnv(data, mode='train')
    print(f"Buy Knowledge RL环境: 动作空间={buy_env.action_space}, "
          f"观察空间={buy_env.observation_space}")
    
    # 测试环境交互
    obs = buy_env.reset()
    print(f"初始观察形状: {obs.shape}")
    
    for _ in range(5):
        action = buy_env.action_space.sample()
        obs, reward, done, info = buy_env.step(action)
        print(f"动作={action}, 奖励={reward}, 完成={done}, 信息={info}")
        if done:
            break
    
    # 创建Sell Knowledge RL环境
    sell_env = SellKnowledgeRLEnv(data, mode='train')
    print(f"\nSell Knowledge RL环境: 动作空间={sell_env.action_space}, "
          f"观察空间={sell_env.observation_space}")
    
    # 测试止损规则
    stop_loss = StopLossRule()
    print(f"\n止损规则已创建")