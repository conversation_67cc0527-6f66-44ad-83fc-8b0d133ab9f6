#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试重构后的代码
验证data_preprocessing.py和calculate_stock_factors_test.py的集成是否正常工作
"""

import pandas as pd
import numpy as np
import os
import sys
from datetime import datetime

# 添加路径以便导入模块
sys.path.append('.')
sys.path.append('./tushare_data_cyb')

def test_imports():
    """测试导入是否正常"""
    print("测试导入...")
    try:
        from data_preprocessing import DataPreprocessor
        from tushare_data_cyb.calculate_stock_factors_test import (
            calculate_heikin_ashi,
            calculate_technical_indicators,
            calculate_index_variables,
            calculate_stock_vs_index_variables,
            generate_trading_signals
        )
        print("✓ 所有导入成功")
        return True
    except ImportError as e:
        print(f"✗ 导入失败: {e}")
        return False

def test_individual_functions():
    """测试单个计算函数"""
    print("\n测试单个计算函数...")
    
    # 创建测试数据
    dates = pd.date_range('2023-01-01', periods=100, freq='D')
    test_data = pd.DataFrame({
        'trade_date': dates,
        'ts_code': ['000001.SZ'] * 100,
        'open': np.random.uniform(10, 20, 100),
        'high': np.random.uniform(15, 25, 100),
        'low': np.random.uniform(8, 15, 100),
        'close': np.random.uniform(10, 20, 100),
        'vol': np.random.uniform(1000, 10000, 100),
        'pct_chg': np.random.uniform(-5, 5, 100),
        'pre_close': np.random.uniform(10, 20, 100)
    })
    
    # 确保high >= low, high >= close, low <= close
    test_data['high'] = np.maximum(test_data['high'], test_data[['close', 'low']].max(axis=1))
    test_data['low'] = np.minimum(test_data['low'], test_data[['close', 'high']].min(axis=1))
    
    try:
        from tushare_data_cyb.calculate_stock_factors_test import (
            calculate_heikin_ashi,
            calculate_technical_indicators,
            calculate_index_variables,
            calculate_stock_vs_index_variables,
            generate_trading_signals
        )
        
        # 测试Heikin-Ashi计算
        result = calculate_heikin_ashi(test_data.copy())
        assert 'ha_open' in result.columns, "Heikin-Ashi计算失败：缺少ha_open列"
        assert 'ha_close' in result.columns, "Heikin-Ashi计算失败：缺少ha_close列"
        print("✓ Heikin-Ashi计算正常")
        
        # 测试技术指标计算
        result = calculate_technical_indicators(result)
        assert 'atr' in result.columns, "技术指标计算失败：缺少atr列"
        assert 'return' in result.columns, "技术指标计算失败：缺少return列"
        print("✓ 技术指标计算正常")
        
        # 测试交易信号生成
        result = generate_trading_signals(result)
        assert 'buy_signal' in result.columns, "交易信号生成失败：缺少buy_signal列"
        assert 'sell_signal' in result.columns, "交易信号生成失败：缺少sell_signal列"
        print("✓ 交易信号生成正常")
        
        return True
        
    except Exception as e:
        print(f"✗ 单个函数测试失败: {e}")
        return False

def test_data_preprocessor():
    """测试DataPreprocessor类"""
    print("\n测试DataPreprocessor类...")
    
    try:
        from data_preprocessing import DataPreprocessor
        
        # 创建预处理器实例
        preprocessor = DataPreprocessor()
        print("✓ DataPreprocessor实例创建成功")
        
        # 测试归一化函数
        test_data = pd.DataFrame({
            'high': [10, 15, 20],
            'low': [8, 12, 16],
            'close': [9, 14, 18],
            'donchian_upper': [12, 17, 22],
            'donchian_lower': [7, 11, 15],
            'atr': [1, 1.5, 2],
            'dji_atr': [0.5, 0.8, 1.2]
        })
        
        result = preprocessor.normalize_data(test_data.copy())
        assert 'donchian_upper_norm' in result.columns, "归一化失败：缺少donchian_upper_norm列"
        print("✓ 数据归一化正常")
        
        return True
        
    except Exception as e:
        print(f"✗ DataPreprocessor测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试重构后的代码...")
    print("=" * 50)
    
    # 测试导入
    if not test_imports():
        print("\n测试失败：导入错误")
        return False
    
    # 测试单个函数
    if not test_individual_functions():
        print("\n测试失败：单个函数错误")
        return False
    
    # 测试DataPreprocessor
    if not test_data_preprocessor():
        print("\n测试失败：DataPreprocessor错误")
        return False
    
    print("\n" + "=" * 50)
    print("✓ 所有测试通过！重构成功！")
    print("\n重构总结：")
    print("1. ✓ 计算函数已成功从data_preprocessing.py转移到calculate_stock_factors_test.py")
    print("2. ✓ data_preprocessing.py已简化，只负责读取CSV、归一化和准备训练数据")
    print("3. ✓ calculate_stock_factors_test.py已集成新的计算函数")
    print("4. ✓ 所有函数导入和调用正常")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
