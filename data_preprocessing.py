"""
数据预处理模块
实现Pro Trader RL论文中的所有69个输入变量和数据归一化
"""

import pandas as pd
import numpy as np
import talib
from typing import Dict, List, Tuple, Optional
import warnings
import os
from config import *
import logging
from datetime import datetime, timedelta

warnings.filterwarnings('ignore')

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class DataPreprocessor:
    """
    数据预处理器
    负责计算论文中提到的所有69个特征变量
    """
    
    def __init__(self, config: dict = None):
        """初始化数据预处理器"""
        self.config = config or {}
        self.stock_data = None
        self.index_data = None
        self.processed_data = None
        
    def load_data(self) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """加载股票和指数数据"""
        logger.info("加载股票数据...")
        
        # 加载股票日线数据
        stock_daily = pd.read_csv(STOCK_DAILY_FILE)
        stock_daily['trade_date'] = pd.to_datetime(stock_daily['trade_date'], format='%Y%m%d')
        
        # 加载每日基本面数据
        daily_basic = pd.read_csv(DAILY_BASIC_FILE)
        daily_basic['trade_date'] = pd.to_datetime(daily_basic['trade_date'], format='%Y%m%d')
        
        # 加载指数数据（道琼斯指数或中证500）
        index_daily = pd.read_csv(INDEX_DAILY_FILE)
        index_daily['trade_date'] = pd.to_datetime(index_daily['trade_date'], format='%Y%m%d')
        
        # 合并股票数据
        self.stock_data = pd.merge(stock_daily, daily_basic, 
                                   on=['ts_code', 'trade_date'], 
                                   how='left')
        self.index_data = index_daily
        
        logger.info(f"加载完成：股票数据 {len(self.stock_data)} 条，指数数据 {len(self.index_data)} 条")
        return self.stock_data, self.index_data
    
    def calculate_heikin_ashi(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        计算Heikin-Ashi蜡烛图数据
        论文第5页提到的基础变量
        """
        ha_close = (df['open'] + df['high'] + df['low'] + df['close']) / 4
        
        ha_open = pd.Series(index=df.index, dtype='float64')
        ha_open.iloc[0] = (df['open'].iloc[0] + df['close'].iloc[0]) / 2
        
        for i in range(1, len(df)):
            ha_open.iloc[i] = (ha_open.iloc[i-1] + ha_close.iloc[i-1]) / 2
        
        ha_high = pd.concat([df['high'], ha_open, ha_close], axis=1).max(axis=1)
        ha_low = pd.concat([df['low'], ha_open, ha_close], axis=1).min(axis=1)
        
        df['ha_open'] = ha_open
        df['ha_high'] = ha_high
        df['ha_low'] = ha_low
        df['ha_close'] = ha_close
        
        return df
    
    def calculate_technical_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        计算技术指标变量（21个）
        根据论文第5页表2
        """
        # Return - 收益率
        df['return'] = df['close'].pct_change()
        
        # ATR - 平均真实范围（10日）
        df['atr'] = talib.ATR(df['high'], df['low'], df['close'], timeperiod=10)
        
        # Stock 1-12 - ATR与N个月前的比较
        for n in range(1, 13):
            days = n * 21  # 假设每月21个交易日
            df[f'stock_{n}'] = df['atr'] / df['atr'].shift(days)
        
        # Super Trend 14和21
        for period, multiplier in [(14, 2), (21, 1)]:
            atr = talib.ATR(df['high'], df['low'], df['close'], timeperiod=period)
            hl_avg = (df['high'] + df['low']) / 2
            
            upper = hl_avg + multiplier * atr
            lower = hl_avg - multiplier * atr
            
            supertrend = pd.Series(index=df.index, dtype='float64')
            in_uptrend = True
            
            for i in range(period, len(df)):
                if df['close'].iloc[i] <= lower.iloc[i]:
                    in_uptrend = False
                elif df['close'].iloc[i] >= upper.iloc[i]:
                    in_uptrend = True
                
                supertrend.iloc[i] = lower.iloc[i] if in_uptrend else upper.iloc[i]
            
            df[f'super_trend_{period}'] = supertrend
        
        # MFI - 资金流量指数（14日）
        df['mfi'] = talib.MFI(df['high'], df['low'], df['close'], df['vol'], timeperiod=14)
        
        # RSI - 相对强弱指数（14日，使用成交量）
        # 注意：论文中提到使用成交量而不是价格
        volume_change = df['vol'].diff()
        gain = volume_change.where(volume_change > 0, 0)
        loss = -volume_change.where(volume_change < 0, 0)
        
        avg_gain = gain.rolling(window=14).mean()
        avg_loss = loss.rolling(window=14).mean()
        
        rs = avg_gain / (avg_loss + 1e-10)
        df['rsi'] = 100 - (100 / (1 + rs))
        
        # Donchian Channel - 唐奇安通道（20日）
        df['donchian_upper'] = df['high'].rolling(window=20).max()
        df['donchian_lower'] = df['low'].rolling(window=20).min()
        
        # AVG Stock - Stock(N)的平均值
        df['avg_stock'] = df[[f'stock_{n}' for n in [1, 3, 6, 12]]].mean(axis=1)
        
        return df
    
    def calculate_index_variables(self, df: pd.DataFrame, index_df: pd.DataFrame) -> pd.DataFrame:
        """
        计算股票指数变量（13个）
        根据论文第5页表3
        """
        # 对齐指数数据到股票数据的日期
        index_aligned = index_df.set_index('trade_date').reindex(df['trade_date'].unique())
        
        # DJI ATR - 指数的ATR（10日）
        index_aligned['dji_atr'] = talib.ATR(
            index_aligned['high'] if 'high' in index_aligned.columns else index_aligned['close'],
            index_aligned['low'] if 'low' in index_aligned.columns else index_aligned['close'],
            index_aligned['close'],
            timeperiod=10
        )
        
        # Index 1-12 - 指数ATR与N个月前的比较
        for n in range(1, 13):
            days = n * 21
            index_aligned[f'index_{n}'] = index_aligned['dji_atr'] / index_aligned['dji_atr'].shift(days)
        
        # 将指数变量合并到股票数据
        index_vars = index_aligned.reset_index()[['trade_date', 'dji_atr'] + [f'index_{n}' for n in range(1, 13)]]
        df = pd.merge(df, index_vars, on='trade_date', how='left')
        
        return df
    
    def calculate_stock_vs_index_variables(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        计算股票指数对比变量（26个）
        根据论文第5页表4
        """
        # RS - 相对强度（Stock(N) / Index(N)）
        for n in range(1, 13):
            df[f'rs_{n}'] = df[f'stock_{n}'] / (df[f'index_{n}'] + 1e-10)
        
        # RS Average - RS的平均值
        for n in [2, 4, 6, 8, 10, 12]:
            cols = [f'rs_{i}' for i in range(1, n+1)]
            df[f'rs_avg_{n}'] = df[cols].mean(axis=1)
        
        # RS Rate - RS转换为0-100的排名
        for window in [5, 10, 20, 40]:
            df[f'rs_rate_{window}'] = df['rs_1'].rolling(window=window).apply(
                lambda x: pd.Series(x).rank(pct=True).iloc[-1] * 100
            )
        
        # Up Stock / Down Stock - 上涨/下跌股票数量
        # 这需要按日期分组计算
        grouped = df.groupby('trade_date')
        up_down_stats = grouped['return'].apply(
            lambda x: pd.Series({
                'up_stock': (x > 0).sum(),
                'down_stock': (x < 0).sum()
            })
        ).reset_index()
        
        df = pd.merge(df, up_down_stats, on='trade_date', how='left')
        
        return df
    
    def generate_trading_signals(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        生成Donchian Channel交易信号
        根据论文第4页的交易策略信号生成
        """
        # 买入信号：当前高价突破20日最高价
        df['buy_signal'] = (df['high'] > df['donchian_upper'].shift(1)) & \
                          (df['high'].shift(1) <= df['donchian_upper'].shift(2))
        
        # 卖出信号：当前低价跌破20日最低价
        df['sell_signal'] = (df['low'] < df['donchian_lower'].shift(1)) & \
                           (df['low'].shift(1) >= df['donchian_lower'].shift(2))
        
        return df
    
    def normalize_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        数据归一化
        使用论文第6-7页描述的自定义归一化方法
        """
        # Donchian通道归一化（公式1-2）
        df['donchian_upper_norm'] = df['donchian_upper'] / df['high']
        df['donchian_lower_norm'] = df['donchian_lower'] / df['low']
        
        # 价格归一化（公式3-8）
        for col in ['close', 'low', 'high', 'ha_close', 'ha_low', 'ha_high']:
            if col in df.columns:
                df[f'{col}_norm'] = df[col] / df['donchian_upper']
        
        # ATR归一化（公式9-10）
        df['dji_atr_norm'] = df['dji_atr'] / df['dji_atr'].shift(1)
        df['atr_norm'] = df['atr'] / df['atr'].shift(1)
        
        # Index和Stock归一化（公式11-13）
        for n in range(1, 13):
            # Min-Max归一化
            for prefix in ['index', 'stock']:
                col = f'{prefix}_{n}'
                if col in df.columns:
                    min_val = df[col].min()
                    max_val = df[col].max()
                    df[f'{col}_norm'] = (df[col] - min_val) / (max_val - min_val + 1e-10)
        
        # AVG Stock归一化（公式13）
        min_stock = df[[f'stock_{n}' for n in range(1, 13)]].min().min()
        max_stock = df[[f'stock_{n}' for n in range(1, 13)]].max().max()
        df['avg_stock_norm'] = (df['avg_stock'] - min_stock) / (max_stock - min_stock + 1e-10)
        
        # RS和RS AVG归一化（公式14-15）
        rs_cols = [f'rs_{n}' for n in range(1, 13)] + [f'rs_avg_{n}' for n in [2, 4, 6, 8, 10, 12]]
        for col in rs_cols:
            if col in df.columns:
                min_val = df[col].min()
                max_val = df[col].max()
                df[f'{col}_norm'] = (df[col] - min_val) / (max_val - min_val + 1e-10)
        
        # 百分比变量归一化（公式16-18）
        for col in ['rs_rate_5', 'rs_rate_10', 'rs_rate_20', 'rs_rate_40', 'mfi', 'rsi']:
            if col in df.columns:
                df[f'{col}_norm'] = df[col] * 0.01
        
        # Super Trend, Return, Up Stock, Down Stock已经是归一化的值
        
        return df
    
    def prepare_rl_features(self, df: pd.DataFrame) -> np.ndarray:
        """
        准备强化学习的输入特征（69维）
        按照论文要求的顺序组织特征
        """
        feature_columns = []
        
        # 1. 基础变量（9个，但Open和Volume被排除，实际使用7个）
        basic_cols = ['high_norm', 'low_norm', 'close_norm', 
                     'ha_high_norm', 'ha_low_norm', 'ha_close_norm', 'return']
        feature_columns.extend(basic_cols)
        
        # 2. 技术指标变量（21个）
        feature_columns.append('atr_norm')
        feature_columns.extend([f'stock_{n}_norm' for n in range(1, 13)])  # 12个
        feature_columns.extend(['super_trend_14', 'super_trend_21'])  # 2个
        feature_columns.extend(['mfi_norm', 'rsi_norm'])  # 2个
        feature_columns.extend(['donchian_upper_norm', 'donchian_lower_norm'])  # 2个
        feature_columns.append('avg_stock_norm')  # 1个
        
        # 3. 股票指数变量（13个）
        feature_columns.append('dji_atr_norm')
        feature_columns.extend([f'index_{n}_norm' for n in range(1, 13)])  # 12个
        
        # 4. 股票指数对比变量（26个）
        feature_columns.extend([f'rs_{n}_norm' for n in range(1, 13)])  # 12个
        feature_columns.extend([f'rs_avg_{n}_norm' for n in [2, 4, 6, 8, 10, 12]])  # 6个
        feature_columns.extend([f'rs_rate_{n}_norm' for n in [5, 10, 20, 40]])  # 4个
        feature_columns.extend(['up_stock', 'down_stock'])  # 2个
        
        # 确保所有列都存在
        missing_cols = [col for col in feature_columns if col not in df.columns]
        if missing_cols:
            logger.warning(f"缺少以下特征列: {missing_cols}")
            # 用0填充缺失列
            for col in missing_cols:
                df[col] = 0
        
        # 提取特征并转换为numpy数组
        features = df[feature_columns].fillna(0).values
        
        # 验证特征维度
        assert features.shape[1] == 69, f"特征维度应为69，实际为{features.shape[1]}"
        
        return features
    
    def process_all_data(self) -> pd.DataFrame:
        """
        处理所有数据的主函数
        """
        logger.info("开始处理数据...")
        
        # 1. 加载数据
        self.load_data()
        
        # 2. 按股票代码分组处理
        processed_dfs = []
        
        for ts_code, group in self.stock_data.groupby('ts_code'):
            logger.debug(f"处理股票 {ts_code}...")
            
            # 确保数据按日期排序
            group = group.sort_values('trade_date').reset_index(drop=True)
            
            # 跳过数据不足的股票
            if len(group) < 100:
                continue
            
            # 3. 计算Heikin-Ashi
            group = self.calculate_heikin_ashi(group)
            
            # 4. 计算技术指标
            group = self.calculate_technical_indicators(group)
            
            # 5. 计算指数变量
            group = self.calculate_index_variables(group, self.index_data)
            
            # 6. 计算股票指数对比变量
            group = self.calculate_stock_vs_index_variables(group)
            
            # 7. 生成交易信号
            group = self.generate_trading_signals(group)
            
            # 8. 数据归一化
            group = self.normalize_data(group)
            
            # 9. 准备RL特征
            features = self.prepare_rl_features(group)
            group['rl_features'] = features.tolist()
            
            processed_dfs.append(group)
        
        # 合并所有处理后的数据
        self.processed_data = pd.concat(processed_dfs, ignore_index=True)
        
        logger.info(f"数据处理完成，共处理 {len(self.processed_data)} 条记录")
        
        return self.processed_data
    
    def save_processed_data(self, filepath: str = None):
        """保存处理后的数据"""
        if filepath is None:
            filepath = os.path.join(DATA_DIR, 'processed_data.csv')
        
        if self.processed_data is not None:
            self.processed_data.to_csv(filepath, index=False)
            logger.info(f"处理后的数据已保存到 {filepath}")
        else:
            logger.warning("没有处理后的数据可保存")
    
    def get_training_data(self, start_date: str = None, end_date: str = None) -> Tuple[np.ndarray, np.ndarray]:
        """
        获取训练数据
        返回：(特征, 标签)
        """
        if self.processed_data is None:
            self.process_all_data()
        
        # 日期筛选
        if start_date:
            start_date = pd.to_datetime(start_date)
            mask = self.processed_data['trade_date'] >= start_date
            data = self.processed_data[mask]
        else:
            data = self.processed_data
        
        if end_date:
            end_date = pd.to_datetime(end_date)
            data = data[data['trade_date'] <= end_date]
        
        # 只使用有买入信号的数据
        buy_signal_data = data[data['buy_signal'] == True]
        
        # 准备特征和标签
        features = []
        labels = []
        
        for idx, row in buy_signal_data.iterrows():
            # 特征：69维向量
            feature = row['rl_features']
            features.append(feature)
            
            # 标签：计算未来收益率是否超过10%
            ts_code = row['ts_code']
            current_date = row['trade_date']
            future_data = self.processed_data[
                (self.processed_data['ts_code'] == ts_code) &
                (self.processed_data['trade_date'] > current_date)
            ].head(120)  # 未来120天
            
            if len(future_data) > 0:
                # 计算最大收益率
                entry_price = row['close']
                max_return = ((future_data['high'].max() - entry_price) / entry_price) * 100
                label = 1 if max_return >= 10 else 0
            else:
                label = 0
            
            labels.append(label)
        
        return np.array(features), np.array(labels)


# 测试代码
if __name__ == "__main__":
    # 创建数据预处理器实例
    preprocessor = DataPreprocessor()
    
    # 处理所有数据
    processed_data = preprocessor.process_all_data()
    
    # 保存处理后的数据
    preprocessor.save_processed_data()
    
    # 获取训练数据
    X_train, y_train = preprocessor.get_training_data(
        start_date=TRAIN_START_DATE,
        end_date=TRAIN_END_DATE
    )
    
    print(f"训练数据形状: X={X_train.shape}, y={y_train.shape}")
    print(f"标签分布: 0={np.sum(y_train==0)}, 1={np.sum(y_train==1)}")