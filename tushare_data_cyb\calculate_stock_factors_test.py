# coding: utf-8
import pandas as pd
import numpy as np
import datetime
import os
import warnings
from joblib import Parallel, delayed
import multiprocessing
from tqdm.auto import tqdm
from scipy import stats
from scipy.stats import skew, kurtosis, pearsonr
from sklearn.linear_model import LinearRegression
import talib

warnings.filterwarnings('ignore')
warnings.filterwarnings("ignore", category=RuntimeWarning)
# --- 1. 配置参数 ---
STOCK_BASIC_FILE = 'stock_basic_cyb.csv'
STOCK_DAILY_FILE = 'stock_daily_cyb.csv'
DAILY_BASIC_FILE = 'daily_basic_cyb.csv'
INDEX_DAILY_FILE = 'index_daily_cyb.csv'  # 新增：指数日线数据文件
OUTPUT_FILE = 'stock_factors_cyb.csv'

windows = [5, 10, 20, 30]
MIN_OBS_FOR_CALC =  60
N_JOBS = multiprocessing.cpu_count() - 1

# --- 2. 数据加载与预处理函数 ---
def load_index_data(index_file):
    """加载指数数据"""
    try:
        if os.path.exists(index_file):
            index_data = pd.read_csv(index_file, encoding='utf-8')
            # 尝试不同的日期格式
            try:
                # 先尝试YYYYMMDD格式
                index_data['trade_date'] = pd.to_datetime(index_data['trade_date'], format='%Y%m%d')
            except:
                # 如果失败，尝试自动解析
                index_data['trade_date'] = pd.to_datetime(index_data['trade_date'])

            index_data['pct_chg'] = pd.to_numeric(index_data['pct_chg'], errors='coerce')
            index_data = index_data.sort_values('trade_date')
            print(f"成功加载指数数据: {len(index_data)} 条记录")
            print(f"指数数据日期范围: {index_data['trade_date'].min()} 至 {index_data['trade_date'].max()}")
            return index_data
        else:
            print(f"指数数据文件不存在: {index_file}")
            return pd.DataFrame()
    except Exception as e:
        print(f"加载指数数据失败: {e}")
        return pd.DataFrame()

def load_and_preprocess_data(stock_basic_file, stock_daily_file, daily_basic_file):
    """加载所有CSV文件并进行初步预处理 - 优化版本"""
    print("开始加载CSV文件...")

    try:
        # 使用更高效的读取方式
        print("加载股票基本信息...")
        stock_basic = pd.read_csv(stock_basic_file, encoding='utf-8')

        print("加载股票日线数据...")
        daily_cols = ['ts_code', 'trade_date', 'pre_close', 'open', 'high', 'low', 'close', 'pct_chg', 'vol', 'amount']
        stock_daily = pd.read_csv(stock_daily_file, encoding='utf-8', usecols=lambda x: x in daily_cols)

        print("加载每日基本面数据...")
        basic_cols = ['ts_code', 'trade_date', 'pe', 'pb', 'turnover_rate', 'volume_ratio', 'total_mv']
        daily_basic = pd.read_csv(daily_basic_file, encoding='utf-8', usecols=lambda x: x in basic_cols)

    except FileNotFoundError as e:
        print(f"错误：找不到文件 {e.filename}。请确保所有CSV文件都在指定路径下。")
        return None, None, None, None, None
    except Exception as e:
        print(f"加载文件时出错: {e}")
        return None, None, None, None, None

    print("开始数据清洗和类型转换...")

    # --- 数据清洗和类型转换 ---
    # 股票基础信息
    print("处理股票基础信息...")
    if 'list_date' in stock_basic.columns:
        stock_basic['list_date'] = pd.to_datetime(stock_basic['list_date'], format='%Y%m%d', errors='coerce')
    if 'delist_date' in stock_basic.columns:
        stock_basic['delist_date'] = pd.to_datetime(stock_basic['delist_date'], format='%Y%m%d', errors='coerce')
        # 只保留未摘牌的股票基本信息
        stock_basic = stock_basic[stock_basic['delist_date'].isna()]

    # 选择需要的列
    basic_keep_cols = [col for col in ['ts_code', 'name', 'area', 'industry', 'list_date'] if col in stock_basic.columns]
    stock_basic = stock_basic[basic_keep_cols]
    stock_basic = stock_basic.drop_duplicates(subset=['ts_code'])

    # 股票日行情
    print("处理股票日线数据...")
    stock_daily['trade_date'] = pd.to_datetime(stock_daily['trade_date'], format='%Y%m%d')

    # 批量转换数值列
    price_cols = ['pre_close', 'open', 'high', 'low', 'close', 'vol', 'amount', 'pct_chg']
    for col in price_cols:
        if col in stock_daily.columns:
            stock_daily[col] = pd.to_numeric(stock_daily[col], errors='coerce')

    # 过滤无效数据
    stock_daily = stock_daily.dropna(subset=['ts_code', 'trade_date', 'close'])
    stock_daily = stock_daily.sort_values(by=['ts_code', 'trade_date'])

    # 每日基本面数据
    print("处理每日基本面数据...")
    if not daily_basic.empty:
        daily_basic['trade_date'] = pd.to_datetime(daily_basic['trade_date'], format='%Y%m%d')
        # 转换数值列
        numeric_cols = ['pe', 'pb', 'turnover_rate', 'volume_ratio', 'total_mv']
        for col in numeric_cols:
            if col in daily_basic.columns:
                daily_basic[col] = pd.to_numeric(daily_basic[col], errors='coerce')
        daily_basic = daily_basic.sort_values(by=['ts_code', 'trade_date'])

    # --- 合并数据 ---
    print("开始合并数据...")

    # 首先合并股票日线和基本信息
    print("合并股票日线和基本信息...")
    stock_merged = pd.merge(stock_daily, stock_basic, on='ts_code', how='left')

    # 合并每日基本面数据
    print("合并每日基本面数据...")
    if not daily_basic.empty:
        stock_merged = pd.merge(stock_merged, daily_basic, on=['ts_code', 'trade_date'], how='left', suffixes=('', '_basic'))
    else:
        # 如果没有基本面数据，添加空列
        for col in ['pe', 'pb', 'turnover_rate']:
            stock_merged[col] = np.nan

    # 不再合并财务数据

    print("最终排序...")
    stock_merged = stock_merged.sort_values(by=['ts_code', 'trade_date'])

    print(f"数据合并完成！最终数据量: {len(stock_merged)} 行")
    return stock_basic, stock_daily, daily_basic, stock_merged

def calculate_volume_price_momentum_factor(high, low, close, pre_close, volume, pct_chg, window=20):
    """
    因子1: windows日内上涨日量价动量-下跌日量价惩罚
    """
    try:
        # 计算振幅
        amplitude = (high - low) / pre_close

        # 判断上涨日和下跌日
        up_days = pct_chg > 0
        down_days = pct_chg < 0

        # 计算量价动量（成交量 * 振幅）
        volume_price_momentum = volume * amplitude

        # 分别计算上涨日和下跌日的量价动量
        up_momentum = np.where(up_days, volume_price_momentum, 0)
        down_momentum = np.where(down_days, volume_price_momentum, 0)

        # 【核心修改】在创建Series时，传入原始索引 high.index
        up_momentum_sum = pd.Series(up_momentum, index=high.index).rolling(window=window, min_periods=window//2).sum()
        down_momentum_sum = pd.Series(down_momentum, index=high.index).rolling(window=window, min_periods=window//2).sum()

        # 因子 = 上涨日量价动量 - 下跌日量价惩罚
        factor = up_momentum_sum - down_momentum_sum

        return factor.fillna(0)
    except:
        # 【建议】这里最好打印一下错误信息，以便调试
        # import traceback
        # print(traceback.format_exc())
        return pd.Series(0, index=high.index) # 返回的错误Series也应带索引

def calculate_volatility_asymmetry_factor(high, low, close, pre_close, pct_chg, window=20):
    """
    因子2: 波动率不对称因子
    """
    try:
        # 计算振幅
        amplitude = (high - low) / pre_close

        # 判断上涨日和下跌日
        up_days = pct_chg > 0
        down_days = pct_chg < 0

        # 分别计算上涨日和下跌日的振幅
        up_amplitude = np.where(up_days, amplitude, np.nan)
        down_amplitude = np.where(down_days, amplitude, np.nan)

        # 【核心修改】在创建Series时，传入原始索引 high.index
        avg_amp_up = pd.Series(up_amplitude, index=high.index).rolling(window=window, min_periods=window//4).mean()
        avg_amp_down = pd.Series(down_amplitude, index=high.index).rolling(window=window, min_periods=window//4).mean()

        # 波动率不对称因子 = 下跌日平均振幅 / 上涨日平均振幅
        factor = avg_amp_down / (avg_amp_up + 1e-8)  # 避免除零

        return factor.fillna(0)
    except:
        return pd.Series(0, index=high.index)


def calculate_market_downside_excess_return_factor(stock_pct_chg, index_pct_chg, window=20):
    """
    因子4: 市场下行日超额收益因子
    """
    try:
        # 判断市场下行日（指数下跌）
        market_down_days = index_pct_chg < 0

        # 计算超额收益率
        excess_return = stock_pct_chg - index_pct_chg

        # 只在市场下行日计算超额收益
        downside_excess_return = np.where(market_down_days, excess_return, np.nan)

        # 【核心修改】在创建Series时，传入原始索引 stock_pct_chg.index
        factor = pd.Series(downside_excess_return, index=stock_pct_chg.index).rolling(window=window, min_periods=window//4).mean()

        return factor.fillna(0)
    except:
        return pd.Series(0, index=stock_pct_chg.index)


# === 从data_preprocessing.py转移的计算函数 ===

def calculate_heikin_ashi(df: pd.DataFrame) -> pd.DataFrame:
    """
    计算Heikin-Ashi蜡烛图数据
    论文第5页提到的基础变量
    """
    ha_close = (df['open'] + df['high'] + df['low'] + df['close']) / 4

    ha_open = pd.Series(index=df.index, dtype='float64')
    ha_open.iloc[0] = (df['open'].iloc[0] + df['close'].iloc[0]) / 2

    for i in range(1, len(df)):
        ha_open.iloc[i] = (ha_open.iloc[i-1] + ha_close.iloc[i-1]) / 2

    ha_high = pd.concat([df['high'], ha_open, ha_close], axis=1).max(axis=1)
    ha_low = pd.concat([df['low'], ha_open, ha_close], axis=1).min(axis=1)

    df['ha_open'] = ha_open
    df['ha_high'] = ha_high
    df['ha_low'] = ha_low
    df['ha_close'] = ha_close

    return df


def calculate_technical_indicators(df: pd.DataFrame) -> pd.DataFrame:
    """
    计算技术指标变量（21个）
    根据论文第5页表2
    """
    # Return - 收益率
    df['return'] = df['close'].pct_change()

    # ATR - 平均真实范围（10日）
    df['atr'] = talib.ATR(df['high'], df['low'], df['close'], timeperiod=10)

    # Stock 1-12 - ATR与N个月前的比较
    for n in range(1, 13):
        days = n * 21  # 假设每月21个交易日
        df[f'stock_{n}'] = df['atr'] / df['atr'].shift(days)

    # Super Trend 14和21
    for period, multiplier in [(14, 2), (21, 1)]:
        atr = talib.ATR(df['high'], df['low'], df['close'], timeperiod=period)
        hl_avg = (df['high'] + df['low']) / 2

        upper = hl_avg + multiplier * atr
        lower = hl_avg - multiplier * atr

        supertrend = pd.Series(index=df.index, dtype='float64')
        in_uptrend = True

        for i in range(period, len(df)):
            if df['close'].iloc[i] <= lower.iloc[i]:
                in_uptrend = False
            elif df['close'].iloc[i] >= upper.iloc[i]:
                in_uptrend = True

            supertrend.iloc[i] = lower.iloc[i] if in_uptrend else upper.iloc[i]

        df[f'super_trend_{period}'] = supertrend

    # MFI - 资金流量指数（14日）
    df['mfi'] = talib.MFI(df['high'], df['low'], df['close'], df['vol'], timeperiod=14)

    # RSI - 相对强弱指数（14日，使用成交量）
    # 注意：论文中提到使用成交量而不是价格
    volume_change = df['vol'].diff()
    gain = volume_change.where(volume_change > 0, 0)
    loss = -volume_change.where(volume_change < 0, 0)

    avg_gain = gain.rolling(window=14).mean()
    avg_loss = loss.rolling(window=14).mean()

    rs = avg_gain / (avg_loss + 1e-10)
    df['rsi'] = 100 - (100 / (1 + rs))

    # Donchian Channel - 唐奇安通道（20日）
    df['donchian_upper'] = df['high'].rolling(window=20).max()
    df['donchian_lower'] = df['low'].rolling(window=20).min()

    # AVG Stock - Stock(N)的平均值
    df['avg_stock'] = df[[f'stock_{n}' for n in [1, 3, 6, 12]]].mean(axis=1)

    return df


def calculate_index_variables(df: pd.DataFrame, index_df: pd.DataFrame) -> pd.DataFrame:
    """
    计算股票指数变量（13个）
    根据论文第5页表3
    """
    # 对齐指数数据到股票数据的日期
    index_aligned = index_df.set_index('trade_date').reindex(df['trade_date'].unique())

    # DJI ATR - 指数的ATR（10日）
    index_aligned['dji_atr'] = talib.ATR(
        index_aligned['high'] if 'high' in index_aligned.columns else index_aligned['close'],
        index_aligned['low'] if 'low' in index_aligned.columns else index_aligned['close'],
        index_aligned['close'],
        timeperiod=10
    )

    # Index 1-12 - 指数ATR与N个月前的比较
    for n in range(1, 13):
        days = n * 21
        index_aligned[f'index_{n}'] = index_aligned['dji_atr'] / index_aligned['dji_atr'].shift(days)

    # 将指数变量合并到股票数据
    index_vars = index_aligned.reset_index()[['trade_date', 'dji_atr'] + [f'index_{n}' for n in range(1, 13)]]
    df = pd.merge(df, index_vars, on='trade_date', how='left')

    return df


def calculate_stock_vs_index_variables(df: pd.DataFrame) -> pd.DataFrame:
    """
    计算股票指数对比变量（26个）
    根据论文第5页表4
    """
    # RS - 相对强度（Stock(N) / Index(N)）
    for n in range(1, 13):
        df[f'rs_{n}'] = df[f'stock_{n}'] / (df[f'index_{n}'] + 1e-10)

    # RS Average - RS的平均值
    for n in [2, 4, 6, 8, 10, 12]:
        cols = [f'rs_{i}' for i in range(1, n+1)]
        df[f'rs_avg_{n}'] = df[cols].mean(axis=1)

    # RS Rate - RS转换为0-100的排名
    for window in [5, 10, 20, 40]:
        df[f'rs_rate_{window}'] = df['rs_1'].rolling(window=window).apply(
            lambda x: pd.Series(x).rank(pct=True).iloc[-1] * 100
        )

    # Up Stock / Down Stock - 上涨/下跌股票数量
    # 对于单个股票，这些值基于该股票的收益率
    if 'return' in df.columns:
        df['up_stock'] = (df['return'] > 0).astype(int)
        df['down_stock'] = (df['return'] < 0).astype(int)
    else:
        # 如果没有return列，设置默认值
        df['up_stock'] = 0
        df['down_stock'] = 0

    return df


def generate_trading_signals(df: pd.DataFrame) -> pd.DataFrame:
    """
    生成Donchian Channel交易信号
    根据论文第4页的交易策略信号生成
    """
    # 买入信号：当前高价突破20日最高价
    df['buy_signal'] = (df['high'] > df['donchian_upper'].shift(1)) & \
                      (df['high'].shift(1) <= df['donchian_upper'].shift(2))

    # 卖出信号：当前低价跌破20日最低价
    df['sell_signal'] = (df['low'] < df['donchian_lower'].shift(1)) & \
                       (df['low'].shift(1) >= df['donchian_lower'].shift(2))

    return df




# --- 11. 主函数：计算所有因子 ---
def calculate_factors_for_stock(stock_data, current_date=None, index_data=None):
    """为单个ts_code的股票数据计算所有因子"""
    ts_code = stock_data['ts_code'].iloc[0]

    # 确保数据按日期排序
    stock_data = stock_data.sort_values(by='trade_date')

    # 检查数据量是否足够进行计算
    if len(stock_data) < MIN_OBS_FOR_CALC:
        # print(f"数据不足，跳过 {ts_code} (需要 {MIN_OBS_FOR_CALC} 天, 只有 {len(stock_data)} 天)")
        return None

    # 创建结果字典
    results_dict = {}

    # --- 1. 基础数据 ---
    results_dict['ts_code'] = stock_data['ts_code']
    results_dict['trade_date'] = stock_data['trade_date']
    results_dict['open'] = stock_data['open']
    results_dict['high'] = stock_data['high']
    results_dict['low'] = stock_data['low']
    results_dict['close'] = stock_data['close']
    results_dict['pct_chg'] = stock_data['pct_chg']
    results_dict['vol'] = stock_data['vol']
    results_dict['amount'] = stock_data['amount']


    if 'industry' in stock_data.columns:
        industry_value = stock_data['industry'].iloc[0]
        results_dict['industry'] = pd.Series(industry_value, index=stock_data.index)
    else:
        results_dict['industry'] = pd.Series('', index=stock_data.index)

    # --- 准备基础的Pandas Series ---
    close = stock_data['close'].astype(float)
    high = stock_data['high'].astype(float)
    low = stock_data['low'].astype(float)
    open_price = stock_data['open'].astype(float)
    volume = stock_data['vol'].astype(float)
    amount = stock_data['amount'].astype(float)
    pct_chg_series = stock_data['pct_chg'].astype(float) # 改个名字以示区分
    pre_close = stock_data['pre_close'].astype(float)
    pe = stock_data.get('pe', pd.Series(np.nan, index=stock_data.index)).astype(float).fillna(0)
    pb = stock_data.get('pb', pd.Series(np.nan, index=stock_data.index)).astype(float).fillna(0)
    turnover_rate = stock_data.get('turnover_rate', pd.Series(np.nan, index=stock_data.index)).astype(float).fillna(0)
    results_dict['pe'] = pe
    results_dict['pb'] = pb
    # 收益率序列 (这是一个核心序列，确保是Series)
    returns = close.pct_change().fillna(0)

    # 对齐的指数收益率序列 (如果存在)，确保是Series
    index_returns = pd.Series(0.0, index=stock_data.index) # 默认值为0的Series
    if index_data is not None and not index_data.empty:
        try:
            stock_dates = pd.to_datetime(stock_data['trade_date'])
            index_data_copy = index_data.copy()
            index_data_copy['trade_date'] = pd.to_datetime(index_data_copy['trade_date'])
            index_aligned = index_data_copy.set_index('trade_date').reindex(stock_dates)
            
            # 【【【核心修正点 1】】】: 删除 .values，保持为Pandas Series
            # 将pct_chg从百分比转为小数
            index_returns = (index_aligned['pct_chg'].fillna(0) / 100)
            # 再次确认填充所有可能的NaN
            index_returns = index_returns.fillna(0)

        except Exception as e:
            print(f"对齐指数数据时出错 for {ts_code}: {e}")
            # 如果出错，确保返回的是一个有索引的0序列
            index_returns = pd.Series(0.0, index=stock_data.index)
            


    temp_df = stock_data.copy()

    # 计算Heikin-Ashi
    temp_df = calculate_heikin_ashi(temp_df)

    # 计算技术指标
    temp_df = calculate_technical_indicators(temp_df)

    # 计算指数变量（如果有指数数据）
    if index_data is not None and not index_data.empty:
        temp_df = calculate_index_variables(temp_df, index_data)
    else:
        # 如果没有指数数据，创建默认的指数变量
        temp_df['dji_atr'] = 0
        for n in range(1, 13):
            temp_df[f'index_{n}'] = 1  # 默认值为1，避免除零错误

    # 计算股票指数对比变量
    temp_df = calculate_stock_vs_index_variables(temp_df)

    # 生成交易信号
    temp_df = generate_trading_signals(temp_df)

    # 将计算结果添加到results_dict中
    # Heikin-Ashi数据
    results_dict['ha_open'] = temp_df.get('ha_open', pd.Series(0, index=stock_data.index))
    results_dict['ha_high'] = temp_df.get('ha_high', pd.Series(0, index=stock_data.index))
    results_dict['ha_low'] = temp_df.get('ha_low', pd.Series(0, index=stock_data.index))
    results_dict['ha_close'] = temp_df.get('ha_close', pd.Series(0, index=stock_data.index))

    # 技术指标
    results_dict['return'] = temp_df.get('return', pd.Series(0, index=stock_data.index))
    results_dict['atr'] = temp_df.get('atr', pd.Series(0, index=stock_data.index))

    # Stock 1-12
    for n in range(1, 13):
        results_dict[f'stock_{n}'] = temp_df.get(f'stock_{n}', pd.Series(0, index=stock_data.index))

    # Super Trend
    results_dict['super_trend_14'] = temp_df.get('super_trend_14', pd.Series(0, index=stock_data.index))
    results_dict['super_trend_21'] = temp_df.get('super_trend_21', pd.Series(0, index=stock_data.index))

    # MFI和RSI
    results_dict['mfi'] = temp_df.get('mfi', pd.Series(0, index=stock_data.index))
    results_dict['rsi'] = temp_df.get('rsi', pd.Series(0, index=stock_data.index))

    # Donchian Channel
    results_dict['donchian_upper'] = temp_df.get('donchian_upper', pd.Series(0, index=stock_data.index))
    results_dict['donchian_lower'] = temp_df.get('donchian_lower', pd.Series(0, index=stock_data.index))

    # AVG Stock
    results_dict['avg_stock'] = temp_df.get('avg_stock', pd.Series(0, index=stock_data.index))

    # 指数变量
    results_dict['dji_atr'] = temp_df.get('dji_atr', pd.Series(0, index=stock_data.index))
    for n in range(1, 13):
        results_dict[f'index_{n}'] = temp_df.get(f'index_{n}', pd.Series(0, index=stock_data.index))

    # RS变量
    for n in range(1, 13):
        results_dict[f'rs_{n}'] = temp_df.get(f'rs_{n}', pd.Series(0, index=stock_data.index))

    # RS Average
    for n in [2, 4, 6, 8, 10, 12]:
        results_dict[f'rs_avg_{n}'] = temp_df.get(f'rs_avg_{n}', pd.Series(0, index=stock_data.index))

    # RS Rate
    for window in [5, 10, 20, 40]:
        results_dict[f'rs_rate_{window}'] = temp_df.get(f'rs_rate_{window}', pd.Series(0, index=stock_data.index))

    # Up/Down Stock
    results_dict['up_stock'] = temp_df.get('up_stock', pd.Series(0, index=stock_data.index))
    results_dict['down_stock'] = temp_df.get('down_stock', pd.Series(0, index=stock_data.index))

    # 交易信号
    results_dict['buy_signal'] = temp_df.get('buy_signal', pd.Series(False, index=stock_data.index))
    results_dict['sell_signal'] = temp_df.get('sell_signal', pd.Series(False, index=stock_data.index))


    # --- 创建最终的DataFrame ---
    results_df = pd.DataFrame(results_dict, index=stock_data.index)
    pd.set_option('future.no_silent_downcasting', True)  # 禁用 silent downcasting
    results_df = results_df.replace([np.inf, -np.inf], np.nan).fillna(0)


    if len(results_df) > MIN_OBS_FOR_CALC:
        return results_df.iloc[MIN_OBS_FOR_CALC-1:-5]
    else:
        return None


# --- 12. 主程序 ---
if __name__ == '__main__':
    print("开始执行创业板股票因子计算...")
    start_time = datetime.datetime.now()

    # --- 加载和预处理数据 ---
    print("加载并预处理数据...")
    stock_basic, stock_daily, daily_basic, stock_merged = load_and_preprocess_data(
        STOCK_BASIC_FILE, STOCK_DAILY_FILE, DAILY_BASIC_FILE
    )

    if stock_merged is None:
        print("数据加载或预处理失败，程序终止。")
        exit()

    print(f"原始数据加载完成。股票行情数据 {len(stock_daily)} 条，合并后数据 {len(stock_merged)} 条。")

    # --- 加载指数数据（用于计算市场下行日因子） ---
    print("加载指数数据...")
    index_data = load_index_data(INDEX_DAILY_FILE)
    if not index_data.empty:
        print(f"指数数据加载完成: {len(index_data)} 条记录")
    else:
        print("警告: 指数数据加载失败，市场下行日因子将填充为0")

    # --- 增量更新处理 ---
    existing_data = None
    last_dates = {}
    if os.path.exists(OUTPUT_FILE):
        print(f"检测到已存在的结果文件: {OUTPUT_FILE}，将进行增量更新。")
        try:
            existing_data = pd.read_csv(OUTPUT_FILE, encoding='utf-8', parse_dates=['trade_date'])
            # 找到每个ts_code已计算的最新日期
            if not existing_data.empty:
                last_dates = existing_data.groupby('ts_code')['trade_date'].max().to_dict()
                print(f"已加载 {len(existing_data)} 条历史计算结果。")
            else:
                print("历史结果文件为空。")
        except Exception as e:
            print(f"加载历史结果文件失败: {e}。将重新计算所有数据。")
            existing_data = None # 出错则重新计算
            last_dates = {}
    else:
        print("未找到历史结果文件，将计算所有数据。")

    # 过滤需要计算的数据
    if last_dates:
        unique_ts_codes_in_merged = stock_merged['ts_code'].unique()
        codes_to_process = []
        for code in unique_ts_codes_in_merged:
            last_calculated_date = last_dates.get(code)
            max_date_for_code = stock_merged[stock_merged['ts_code'] == code]['trade_date'].max()
            if last_calculated_date is None or max_date_for_code > last_calculated_date:
                 codes_to_process.append(code)

        if not codes_to_process:
            print("没有新的数据需要计算。")
            exit()

        print(f"需要处理或更新 {len(codes_to_process)} 个股票的数据。")
        data_to_process = stock_merged[stock_merged['ts_code'].isin(codes_to_process)]

    else:
        # 如果没有历史数据，处理所有数据
        data_to_process = stock_merged
        codes_to_process = data_to_process['ts_code'].unique()
        print("将计算所有股票的数据。")

    if data_to_process.empty:
         print("筛选后没有数据需要处理。")
         exit()

    # --- 并行计算 ---
    print(f"开始并行计算因子 (使用 {N_JOBS} 个进程)...")
    grouped_data = [group for _, group in data_to_process.groupby('ts_code')]

    results_list = Parallel(n_jobs=N_JOBS, verbose=0)(
        delayed(calculate_factors_for_stock)(stock_group, None, index_data)
        for stock_group in tqdm(grouped_data, desc="计算进度", total=len(grouped_data))
    )

    # --- 合并结果 ---
    print("合并计算结果...")
    valid_results = [res for res in results_list if res is not None and isinstance(res, pd.DataFrame) and not res.empty]

    if not valid_results:
        print("所有股票计算均失败或无有效结果。")
        new_results_df = pd.DataFrame() # 创建空的DataFrame
    else:
        new_results_df = pd.concat(valid_results, ignore_index=True)
        new_results_df = new_results_df.sort_values(by=['ts_code', 'trade_date'])
        print(f"计算完成，得到 {len(new_results_df)} 条新因子数据。")

        # --- 筛选出真正"新"的数据行 ---
        if last_dates:
            rows_to_keep = []
            for code, group in new_results_df.groupby('ts_code'):
                last_calculated_date = last_dates.get(code)
                if last_calculated_date:
                    rows_to_keep.append(group[group['trade_date'] > last_calculated_date])
                else:
                    rows_to_keep.append(group)

            if rows_to_keep:
                 new_results_df_filtered = pd.concat(rows_to_keep, ignore_index=True)
                 print(f"筛选出 {len(new_results_df_filtered)} 条需要添加到结果文件的因子数据。")
            else:
                 new_results_df_filtered = pd.DataFrame() # 可能没有更新的数据
                 print("没有需要添加到结果文件的新日期数据。")
            new_results_df = new_results_df_filtered # 使用筛选后的结果

    # --- 合并新旧数据并保存 ---
    if existing_data is not None and not new_results_df.empty:
        print("合并新计算结果与历史结果...")
        # 获取共同列和所有列
        common_cols = existing_data.columns.intersection(new_results_df.columns).tolist()
        all_cols = existing_data.columns.union(new_results_df.columns).tolist()

        # 重新索引
        existing_data_reindexed = existing_data.reindex(columns=all_cols)
        new_results_df_reindexed = new_results_df.reindex(columns=all_cols)
        # 合并并去重
        final_df = pd.concat([existing_data_reindexed, new_results_df_reindexed], ignore_index=True)
        final_df = final_df.drop_duplicates(subset=['ts_code', 'trade_date'], keep='last')
        final_df = final_df.sort_values(by=['ts_code', 'trade_date'])
    elif not new_results_df.empty:
        print("保存首次计算的结果...")
        final_df = new_results_df.sort_values(by=['ts_code', 'trade_date'])
    elif existing_data is not None:
         print("没有新数据需要更新，保留原始结果文件。")
         final_df = existing_data # 保持原样
    else:
         print("没有计算出任何结果，无法保存文件。")
         final_df = pd.DataFrame() # 创建空的DataFrame避免保存时出错
    '''
    #过滤最近一年的数据
    if not final_df.empty:
        # 获取数据中的最新日期
        latest_date = final_df['trade_date'].max()

        # 计算一年前的日期
        one_year_ago = latest_date - pd.DateOffset(years=1)

        # 过滤出最近一年的数据
        print(f"过滤数据：保留 {one_year_ago.strftime('%Y-%m-%d')} 至 {latest_date.strftime('%Y-%m-%d')} 的数据...")
        final_df_filtered = final_df[final_df['trade_date'] >= one_year_ago].copy()

        # 打印过滤前后的数据量对比
        print(f"过滤前数据量: {len(final_df)} 条")
        print(f"过滤后数据量: {len(final_df_filtered)} 条")
        print(f"删除了 {len(final_df) - len(final_df_filtered)} 条早于一年的历史数据")

        # 更新final_df为过滤后的数据
        final_df = final_df_filtered
    '''
    # 保存最终结果到CSV
    if not final_df.empty:
        try:
            final_df.to_csv(OUTPUT_FILE, index=False, encoding='utf-8-sig') # 使用utf-8-sig确保Excel能正确打开中文
            print(f"结果已成功保存到: {OUTPUT_FILE}")
            print(f"最终文件包含 {len(final_df)} 条记录。")
        except Exception as e:
            print(f"保存结果文件失败: {e}")
    else:
        print("最终结果为空，未生成或更新文件。")

    end_time = datetime.datetime.now()
    print(f"总耗时: {end_time - start_time}")