"""
Pro Trader RL 配置文件
基于论文: Reinforcement learning framework for generating trading knowledge 
by mimicking the decision-making patterns of professional traders
"""

import os
from datetime import datetime

# ==================== 系统配置 ====================
# Python环境路径
PYTHON_PATH = r"D:\ProgramData\miniconda3\envs\v8new\python.exe"

# 项目根目录
PROJECT_ROOT = os.path.dirname(os.path.abspath(__file__))

# 数据目录
DATA_DIR = os.path.join(PROJECT_ROOT, "tushare_data_cyb")

# 模型保存目录
MODEL_DIR = os.path.join(PROJECT_ROOT, "models")
os.makedirs(MODEL_DIR, exist_ok=True)

# 日志目录
LOG_DIR = os.path.join(PROJECT_ROOT, "logs")
os.makedirs(LOG_DIR, exist_ok=True)

# 结果目录
RESULTS_DIR = os.path.join(PROJECT_ROOT, "results")
os.makedirs(RESULTS_DIR, exist_ok=True)

# ==================== 数据配置 ====================
# 数据文件路径
STOCK_BASIC_FILE = os.path.join(DATA_DIR, "stock_basic_cyb.csv")
STOCK_DAILY_FILE = os.path.join(DATA_DIR, "stock_daily_cyb.csv")
DAILY_BASIC_FILE = os.path.join(DATA_DIR, "daily_basic_cyb.csv")
INDEX_DAILY_FILE = os.path.join(DATA_DIR, "index_daily_cyb.csv")
FACTORS_FILE = os.path.join(DATA_DIR, "stock_factors_cyb.csv")

# 数据日期范围
TRAIN_START_DATE = "20150101"  # 训练开始日期
TRAIN_END_DATE = "20221231"    # 训练结束日期
VAL_START_DATE = "20230101"    # 验证开始日期
VAL_END_DATE = "20231231"      # 验证结束日期
TEST_START_DATE = "20240101"   # 测试开始日期
TEST_END_DATE = "20241231"     # 测试结束日期

# ==================== 技术指标参数（论文第5页表1-4） ====================
# 基础变量 (9个)
BASIC_VARIABLES = [
    'open', 'high', 'low', 'close', 'volume',
    'ha_open', 'ha_high', 'ha_low', 'ha_close'
]

# 技术指标变量 (21个)
TECHNICAL_INDICATORS = {
    'return': 1,  # 收益率
    'atr': 10,  # ATR周期
    'stock_n': list(range(1, 13)),  # Stock(1-12)
    'super_trend': [14, 21],  # Super Trend周期
    'mfi': 14,  # MFI周期
    'rsi': 14,  # RSI周期
    'donchian': 20,  # Donchian Channel周期
    'avg_stock': [1, 3, 6, 12]  # AVG Stock周期
}

# 股票指数变量 (13个)
INDEX_VARIABLES = {
    'dji_atr': 10,  # DJI ATR周期
    'index_n': list(range(1, 13))  # Index(1-12)
}

# 股票指数对比变量 (26个)
STOCK_VS_INDEX = {
    'rs': list(range(1, 13)),  # RS(1-12)
    'rs_avg': [2, 4, 6, 8, 10, 12],  # RS AVG
    'rs_rate': [5, 10, 20, 40],  # RS Rate
    'up_stock': True,  # 上涨股票数
    'down_stock': True  # 下跌股票数
}

# 总共69个输入变量
TOTAL_INPUT_FEATURES = 69

# ==================== 交易策略配置 ====================
# Donchian Channel策略参数
DONCHIAN_UPPER_PERIOD = 20  # 上轨周期
DONCHIAN_LOWER_PERIOD = 20  # 下轨周期

# 交易信号阈值
BUY_SIGNAL_THRESHOLD = 0.1  # 买入信号阈值（10%收益率）
SELL_SIGNAL_THRESHOLD = 0.1  # 卖出信号阈值（10%收益率）

# 止损规则参数
STOP_LOSS_ON_DIPS = -0.1  # 下跌止损阈值（-10%）
STOP_LOSS_ON_SIDEWAYS_DAYS = 20  # 横盘止损天数
STOP_LOSS_ON_SIDEWAYS_THRESHOLD = -0.1  # 横盘止损阈值

# 120天窗口期（论文中提到的关键参数）
TRADING_WINDOW = 120  # 交易窗口期

# ==================== 强化学习配置 ====================
# PPO算法超参数（论文第10页表6）
PPO_CONFIG = {
    'learning_rate': 0.0001,  # 学习率
    'n_steps': 2048,  # 每次更新的步数
    'batch_size': 64,  # 批次大小
    'n_epochs': 10,  # 每次更新的epoch数
    'gamma': 0.99,  # 折扣因子
    'gae_lambda': 0.95,  # GAE lambda
    'clip_range': 0.2,  # PPO裁剪范围
    'clip_range_vf': None,  # 价值函数裁剪范围
    'ent_coef': 0.01,  # 熵系数
    'vf_coef': 0.5,  # 价值函数系数
    'max_grad_norm': 0.5,  # 梯度裁剪
    'target_kl': None,  # 目标KL散度
    'tensorboard_log': LOG_DIR,  # TensorBoard日志目录
    'verbose': 1  # 详细程度
}

# Buy Knowledge RL网络结构（论文第7页）
BUY_KNOWLEDGE_RL_NETWORK = {
    'input_dim': 69,  # 输入维度（归一化后的69个特征）
    'hidden_layers': [69, 40, 2],  # 隐藏层结构
    'activation': 'relu',  # 激活函数
    'output_dim': 2  # 输出维度（2个动作概率）
}

# Sell Knowledge RL网络结构（论文第8页）
SELL_KNOWLEDGE_RL_NETWORK = {
    'input_dim': 70,  # 输入维度（69个特征 + 当前收益率）
    'hidden_layers': [70, 40, 2],  # 隐藏层结构
    'activation': 'relu',  # 激活函数
    'output_dim': 2  # 输出维度（卖出/持有概率）
}

# 训练配置
TRAINING_CONFIG = {
    'total_timesteps': 1000000,  # 总训练步数
    'eval_freq': 10000,  # 评估频率
    'save_freq': 50000,  # 模型保存频率
    'log_interval': 100,  # 日志间隔
    'early_stop_patience': 10,  # 早停耐心值
    'n_eval_episodes': 10,  # 评估回合数
}

# ==================== 回测配置 ====================
BACKTEST_CONFIG = {
    'initial_capital': 10000,  # 初始资金（美元）
    'max_stocks': 10,  # 最大持股数量
    'max_position_per_stock': 0.1,  # 单只股票最大仓位（10%）
    'commission_rate': 0.001,  # 交易费率（0.1%）
    'slippage': 0.001,  # 滑点
    'min_trade_amount': 100,  # 最小交易金额
}

# ==================== 评估指标配置 ====================
EVALUATION_METRICS = [
    'annual_return',  # 年化收益率
    'cumulative_return',  # 累计收益率
    'sharpe_ratio',  # 夏普比率
    'sortino_ratio',  # 索提诺比率
    'max_drawdown',  # 最大回撤
    'win_rate',  # 胜率
    'profit_loss_ratio',  # 盈亏比
    'trading_count',  # 交易次数
    'accuracy'  # 准确率（10%以上收益的交易占比）
]

# ==================== 并行计算配置 ====================
PARALLEL_CONFIG = {
    'n_jobs': -1,  # 使用所有可用CPU核心
    'backend': 'multiprocessing',  # 并行后端
    'verbose': 0  # 详细程度
}

# ==================== 日志配置 ====================
LOGGING_CONFIG = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'standard': {
            'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        },
        'detailed': {
            'format': '%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s'
        }
    },
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
            'level': 'INFO',
            'formatter': 'standard',
            'stream': 'ext://sys.stdout'
        },
        'file': {
            'class': 'logging.FileHandler',
            'level': 'DEBUG',
            'formatter': 'detailed',
            'filename': os.path.join(LOG_DIR, f'pro_trader_rl_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
            'mode': 'a'
        }
    },
    'root': {
        'level': 'INFO',
        'handlers': ['console', 'file']
    }
}

# ==================== 随机种子配置 ====================
RANDOM_SEED = 42  # 随机种子，确保结果可重现

# ==================== GPU配置 ====================
USE_GPU = True  # 是否使用GPU
GPU_DEVICE = 0  # GPU设备编号

# ==================== 模型检查点配置 ====================
CHECKPOINT_CONFIG = {
    'save_best': True,  # 保存最佳模型
    'save_last': True,  # 保存最后模型
    'monitor': 'sharpe_ratio',  # 监控指标
    'mode': 'max',  # 最大化还是最小化
    'verbose': 1  # 详细程度
}

# ==================== 数据归一化配置 ====================
NORMALIZATION_CONFIG = {
    'method': 'custom',  # 使用论文中的自定义归一化方法
    'clip_outliers': True,  # 是否裁剪异常值
    'outlier_threshold': 3,  # 异常值阈值（标准差倍数）
}

# ==================== 实验配置 ====================
EXPERIMENT_CONFIG = {
    'name': f'pro_trader_rl_{datetime.now().strftime("%Y%m%d_%H%M%S")}',
    'description': 'Pro Trader RL implementation based on the paper',
    'tags': ['reinforcement_learning', 'stock_trading', 'PPO', 'financial_AI'],
    'track_metrics': True,  # 是否跟踪指标
    'save_artifacts': True,  # 是否保存产物
}